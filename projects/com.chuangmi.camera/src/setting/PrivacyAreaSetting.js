import React from 'react';
import {
  ScrollView,
  View,
  Text,
  Image,
  Dimensions,
  StyleSheet,
} from 'react-native';

import {imiThemeManager} from '../../../../imilab-design-ui';
import ListItem from '../../../../imilab-design-ui/src/widgets/settingUI/ListItem';
import ListItmeWithSwitch from '../../../../imilab-design-ui/src/widgets/settingUI/ListItmeWithSwitch';
import MessageDialog from '../../../../imilab-design-ui/src/widgets/settingUI/MessageDialog';
import I18n from '../../../../globalization/Localize';
import BaseDeviceComponent from '../../../../imilab-rn-sdk/components/BaseDeviceComponent';
import {LetDevice} from '../../../../imilab-rn-sdk/native/iot-kit/IMIDevice';
import {LetIMIIotRequest, IMIGotoPage} from '../../../../imilab-rn-sdk';
import {showToast, showLoading} from '../../../../imilab-design-ui';
import {stringsTo} from '../../../../globalization/Localize';
import NavigationBar from '../../../../imi-rn-commonView/NavigationBar/NavigationBar';

const ScreenOptions = Dimensions.get('window');
const viewWidth = ScreenOptions.width - 24 * 2;
const viewHeight = viewWidth * 9 / 16;
// 将直播流播放器分成8*4个矩形块
const itemWidth = viewWidth / 8;
const itemHeight = viewHeight / 4;
const REACT_MARGIN = 3; // 矩形线框到画面边缘的外边距

const tag = 'PrivacyAreaSetting';

export default class PrivacyAreaSetting extends BaseDeviceComponent {
  constructor(props) {
    super(props);
    this.state = {
      privacyAreaSwitch: false, // 隐私区域保护总开关
      monitorAreaData: null, // 隐私区域坐标数据
      areaType: 0, // 区域样式类型 0-3
      progressing: false,
      showAIDialog: false,
      // 其他AI功能状态（用于提醒冲突）
      fenceSwitch: false,
      peopleSwitch: false,
      motionSwitch: false,
    };
    // 矩形线框的左上角和右下键的x、y坐标轴
    this.rectDatas = [REACT_MARGIN, REACT_MARGIN, viewWidth - REACT_MARGIN, viewHeight - REACT_MARGIN];
  }

  componentDidMount() {
    this._getAllData();
    this._subscribeFocus = this.props.navigation.addListener('focus', () => {
      // 从编辑页面返回时刷新数据
      this._getAllData();
    });
  }

  componentWillUnmount() {
    this._subscribeFocus && this._subscribeFocus();
    showLoading(false);
  }

  // 获取隐私区域保护相关数据
  async _getAllData() {
    this.setState({ progressing: true });
    showLoading(stringsTo('commWaitText'), true);

    try {
      // 暂时使用模拟数据来避免加载阻塞，实际使用时需要替换为真实的物模型ID
      console.log('开始获取隐私区域设置数据...');

      // 模拟异步数据加载
      await new Promise(resolve => setTimeout(resolve, 500));

      // 使用模拟数据初始化页面 - 显示已配置的隐私区域
      let newStates = {
        progressing: false,
        privacyAreaSwitch: true, // 模拟隐私区域已开启
        monitorAreaData: {
          pos: "[25, 30],[75, 70]", // 模拟已配置的区域坐标
          style: 2, // 模拟样式类型
          display: false
        },
        areaType: 2, // 使用可爱2样式类型
        motionSwitch: true, // 模拟运动检测已开启
        peopleSwitch: false, // 模拟人形检测关闭
        fenceSwitch: true // 模拟围栏检测已开启
      };

      // 设置合理的区域坐标（在有效绘制范围内）
      // 计算安全的坐标范围：左上角(50, 40)，右下角(viewWidth-50, viewHeight-40)
      const safeMargin = 50;
      this.rectDatas = [
        safeMargin,
        40,
        Math.min(viewWidth - safeMargin, 200),
        Math.min(viewHeight - 40, 120)
      ];

      // TODO: 当物模型ID确定后，启用真实的网络请求
      /*
      // 获取隐私区域开关状态
      const privacySwitchParams = {
        Path: '/v1.0/imilab-01/device/control/property/getByCached',
        ParamMap: {
          iotId: LetDevice.deviceID,
          thingId: '10025', // 需要替换为实际的隐私区域开关物模型ID
          method: 'sync',
        },
        Method: 'POST',
      };

      // 获取隐私区域参数
      const privacyAreaParams = {
        Path: '/v1.0/imilab-01/device/control/property/getByCached',
        ParamMap: {
          iotId: LetDevice.deviceID,
          thingId: '10026', // 需要替换为实际的隐私区域参数物模型ID
          method: 'sync',
        },
        Method: 'POST',
      };

      // 获取其他AI功能状态以检查冲突
      const otherFeaturesParams = [
        {
          Path: '/v1.0/imilab-01/device/control/property/getByCached',
          ParamMap: {
            iotId: LetDevice.deviceID,
            thingId: '10021', // 移动侦测
            method: 'sync',
          },
          Method: 'POST',
        },
        {
          Path: '/v1.0/imilab-01/device/control/property/getByCached',
          ParamMap: {
            iotId: LetDevice.deviceID,
            thingId: '10022', // 人形侦测
            method: 'sync',
          },
          Method: 'POST',
        }
      ];

      const results = await Promise.all([
        LetIMIIotRequest.sendUserServerRequest(privacySwitchParams, true),
        LetIMIIotRequest.sendUserServerRequest(privacyAreaParams, true),
        ...otherFeaturesParams.map(param => LetIMIIotRequest.sendUserServerRequest(param, true))
      ]);
      
      // 解析隐私区域开关状态
      if (results[0]?.value?.value !== undefined) {
        newStates.privacyAreaSwitch = results[0].value.value;
      }

      // 解析隐私区域参数
      if (results[1]?.value?.value) {
        try {
          const areaParams = JSON.parse(results[1].value.value);
          newStates.monitorAreaData = areaParams.area;
          newStates.areaType = areaParams.style || 0;
        } catch (e) {
          console.log('解析隐私区域参数失败:', e);
        }
      }

      // 解析其他AI功能状态
      newStates.motionSwitch = results[2]?.value?.value || false;
      newStates.peopleSwitch = results[3]?.value?.value || false;
      */

      this.setState(newStates, () => {
        if (this.state.monitorAreaData && this.state.monitorAreaData.pos) {
          this._parseAreaDataValue(this.state.monitorAreaData.pos);
        }
      });

      console.log('隐私区域设置数据加载完成');

    } catch (error) {
      console.log('获取隐私区域设置失败:', error);
      showToast(stringsTo('commLoadingFailText'));
      this.setState({ progressing: false });
    } finally {
      showLoading(false);
    }
  }

  // 解析区域坐标数据并转换为UI坐标
  _parseAreaDataValue(coordsArrayString) {
    try {
      let coordsStringArray = coordsArrayString.replace("],", "]_").split('_');
      let coordsArray = [...JSON.parse(coordsStringArray[0]), ...JSON.parse(coordsStringArray[1])];
      coordsArray = [
        coordsArray[0] / 100.0 * viewWidth, 
        coordsArray[1] / 100.0 * viewHeight,
        coordsArray[2] / 100.0 * viewWidth, 
        coordsArray[3] / 100.0 * viewHeight
      ];

      // 修正边界误差
      coordsArray[0] < REACT_MARGIN ? coordsArray[0] = REACT_MARGIN : null;
      coordsArray[1] < REACT_MARGIN ? coordsArray[1] = REACT_MARGIN : coordsArray[1] - 1.5;
      coordsArray[2] > viewWidth - REACT_MARGIN ? coordsArray[2] = viewWidth - REACT_MARGIN : coordsArray[2] = coordsArray[2] - 2;
      coordsArray[3] > viewHeight - REACT_MARGIN ? coordsArray[3] = viewHeight - REACT_MARGIN : coordsArray[3] = coordsArray[3] - 1.5;
      
      this.rectDatas = coordsArray;
    } catch (e) {
      console.log('解析区域坐标失败:', e);
    }
  }

  // 隐私区域保护开关变更
  _onPrivacyAreaSwitchChange(value) {
    // 检查是否与其他AI功能冲突
    if (value && (this.state.fenceSwitch || this.state.peopleSwitch || this.state.motionSwitch)) {
      this.setState({ showAIDialog: true });
      return;
    }

    showLoading(stringsTo('commWaitText'), true);
    
    // 暂时使用模拟设置，实际使用时需要替换为真实的物模型请求
    setTimeout(() => {
      this.setState({ privacyAreaSwitch: value });
      showToast(stringsTo('settings_set_success'));
      showLoading(false);
      console.log('隐私区域开关设置为:', value);
    }, 300);

    // TODO: 当物模型ID确定后，启用真实的设备属性设置
    /*
    const paramJson = JSON.stringify({value});
    
    LetDevice.setProperties(true, LetDevice.deviceID, '10025', paramJson) // 需要替换为实际的物模型ID
      .then(() => {
        this.setState({ privacyAreaSwitch: value });
        showToast(stringsTo('settings_set_success'));
      })
      .catch(e => {
        console.log('设置隐私区域开关失败:', e);
        this.setState({ privacyAreaSwitch: !value });
        showToast(I18n.t('operationFailed'));
      })
      .finally(() => {
        showLoading(false);
      });
    */
  }

  // 从区域编辑页面返回的回调
  _refreshEffectiveMonitorArea(rectangleCoords, areaType) {
    this.rectDatas = rectangleCoords;
    this.setState({ areaType });
  }

  render() {
    return (
      <View style={styles.container}>
        <NavigationBar
          title={stringsTo('privacy_area_protection')}
          left={[
            {
              key: NavigationBar.ICON.BACK,
              onPress: () => {
                this.props.navigation.canGoBack() ? this.props.navigation.goBack() : IMIGotoPage.exit();
              },
              accessibilityLabel: 'privacy_area_back',
            },
          ]}
          right={[]}
        />

        <ScrollView showsVerticalScrollIndicator={false}>
          <ListItmeWithSwitch
            title={stringsTo('privacy_area_protection')}
            value={this.state.privacyAreaSwitch}
            onValueChange={(value) => this._onPrivacyAreaSwitchChange(value)}
            accessibilityLabel={['privacy_area_on', 'privacy_area_off']}
          />
          
          {this.state.privacyAreaSwitch ? (
            <ListItem
              title={stringsTo('area_privacy_edit')}
              onPress={() => {
                // 模拟围栏区域数据（应该比隐私区域大）
                const fenceAreaData = [30, 20, 250, 150];

                this.props.navigation.push('PrivacyAreaModifyPage', {
                  areaData: [...this.rectDatas], // 隐私区域坐标
                  areaType: this.state.areaType,
                  fenceSwitch: this.state.fenceSwitch,
                  fenceData: fenceAreaData, // 围栏区域坐标
                  callback: (areaDataNew, type) => this._refreshEffectiveMonitorArea([...areaDataNew], type)
                });
              }}
              accessibilityLabel={'privacy_area_edit'}
            />
          ) : null}
          
          <View style={{ height: 40 }} />
        </ScrollView>

        {this._renderAIDialog()}
      </View>
    );
  }


  // AI功能冲突提醒对话框
  _renderAIDialog() {
    return (
      <MessageDialog
        visible={this.state.showAIDialog}
        title={stringsTo('tips')}
        canDismiss={true}
        onDismiss={() => {
          this.setState({ showAIDialog: false });
        }}
        buttons={[
          {
            text: I18n.t('ok_button'),
            callback: () => {
              this.setState({ showAIDialog: false });
            },
          },
        ]}>
        <Text style={styles.dialogText}>
          {stringsTo('private_open_msg')}
        </Text>
      </MessageDialog>
    );
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: imiThemeManager.theme.pageBg,
  },
  dialogText: {
    fontSize: 14,
    color: imiThemeManager.theme.textColor,
    lineHeight: 20,
    textAlign: 'center',
  },
});