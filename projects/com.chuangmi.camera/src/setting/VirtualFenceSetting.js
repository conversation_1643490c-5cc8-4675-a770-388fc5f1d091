import {View, StyleSheet, ScrollView} from 'react-native';
import React, {useEffect, useState} from 'react';
import NavigationBar from '../../../../imi-rn-commonView/NavigationBar/NavigationBar';
import {IMIGotoPage} from '../../../../imilab-rn-sdk';
import {imiThemeManager} from '../../../../imilab-design-ui';
import ListItmeWithSwitch from '../../../../imilab-design-ui/src/widgets/settingUI/ListItmeWithSwitch';
import ListItem from '../../../../imilab-design-ui/src/widgets/settingUI/ListItem';
import {stringsTo} from '../../../../globalization/Localize';

const VirtualFenceSetting = props => {
  // 使用模拟数据显示已配置的围栏设置
  const [virtualFenceEnabled, setVirtualFenceEnabled] = useState(true); // 模拟围栏已开启
  const [personEnterFence, setPersonEnterFence] = useState(true); // 模拟进入检测已开启
  const [personLeaveFence, setPersonLeaveFence] = useState(true); // 模拟离开检测也已开启
  const [showVirtualFence, setShowVirtualFence] = useState(true); // 模拟显示围栏已开启
  // 设置合理的围栏区域坐标（在有效绘制范围内）
  const [fenceAreaData, setFenceAreaData] = useState([30, 20, 250, 150]);

  useEffect(() => {
    // 模拟初始化数据加载
    console.log('虚拟围栏设置页面已加载，当前配置:');
    console.log('- 围栏开关:', virtualFenceEnabled);
    console.log('- 进入检测:', personEnterFence);
    console.log('- 离开检测:', personLeaveFence);
    console.log('- 显示围栏:', showVirtualFence);
    console.log('- 围栏区域:', fenceAreaData);
  }, []);

  const handleVirtualFenceChange = (value) => {
    setVirtualFenceEnabled(value);
    console.log('虚拟围栏开关:', value);
  };

  const handlePersonEnterChange = (value) => {
    setPersonEnterFence(value);
    console.log('有人进入围栏:', value);
  };

  const handlePersonLeaveChange = (value) => {
    setPersonLeaveFence(value);
    console.log('有人离开围栏:', value);
  };

  const handleShowVirtualFenceChange = (value) => {
    setShowVirtualFence(value);
    console.log('显示虚拟围栏:', value);
  };

  const handleFenceAreaPress = () => {
    // 跳转到围栏区域设置页面
    console.log('跳转到围栏区域设置');
    // 模拟隐私区域数据（应该在围栏内部）
    const privacyAreaData = [50, 40, 200, 120];

    props.navigation.push('VirtualFenceAreaSetting', {
      areaData: fenceAreaData, // 围栏区域坐标
      privateSwitch: true, // 隐私区域开关
      privateAreaData: privacyAreaData, // 隐私区域坐标
      styleType: 2, // 可爱2样式
      callback: (areaData, areaType) => {
        console.log('围栏区域编辑回调:', areaData, areaType);
        setFenceAreaData(areaData); // 更新围栏区域数据
      }
    });
  };

  return (
    <View style={styles.container}>
      <NavigationBar
        title={stringsTo("virtual_Fence")}
        left={[
          {
            key: NavigationBar.ICON.BACK,
            onPress: () => {
              props.navigation.goBack() ? props.navigation.goBack() : IMIGotoPage.exit();
            },
            accessibilityLabel: 'virtual_fence_back',
          },
        ]}
        right={[]}
      />
      
      <ScrollView style={styles.scrollView}>
        {/* 设置选项区域 */}
        <View style={styles.settingsSection}>
          {/* 虚拟围栏主开关 */}
          <ListItmeWithSwitch 
            title="虚拟围栏"
            value={virtualFenceEnabled}
            onValueChange={handleVirtualFenceChange}
            accessibilityLabel={['virtual_fence_off', 'virtual_fence_on']}
          />
          
          {/* 有人进入围栏 */}
          {virtualFenceEnabled && (
            <ListItmeWithSwitch 
              title="有人进入围栏"
              subtitle="当有人进入围栏时，拍摄视频并推送"
              value={personEnterFence}
              onValueChange={handlePersonEnterChange}
              accessibilityLabel={['person_enter_off', 'person_enter_on']}
            />
          )}
          
          {/* 有人离开围栏 */}
          {virtualFenceEnabled && (
            <ListItmeWithSwitch 
              title="有人离开围栏"
              subtitle="当有人离开围栏时，拍摄视频并推送"
              value={personLeaveFence}
              onValueChange={handlePersonLeaveChange}
              accessibilityLabel={['person_leave_off', 'person_leave_on']}
            />
          )}
          
          {/* 围栏区域设置 */}
          {virtualFenceEnabled && (
            <ListItem
              title="围栏区域"
              subtitle={`已设置区域: ${Math.round((fenceAreaData[2] - fenceAreaData[0]) * (fenceAreaData[3] - fenceAreaData[1]) / 10000 * 100)}% 画面`}
              onPress={handleFenceAreaPress}
              accessibilityLabel="fence_area_setting"
            />
          )}
          
          {/* 显示虚拟围栏 */}
          {virtualFenceEnabled && (
            <ListItmeWithSwitch 
              title="显示虚拟围栏"
              value={showVirtualFence}
              onValueChange={handleShowVirtualFenceChange}
              accessibilityLabel={['show_fence_off', 'show_fence_on']}
            />
          )}
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: imiThemeManager.theme.pageBg || '#FFFFFF',
  },
  scrollView: {
    flex: 1,
  },
  settingsSection: {
    marginTop: 20,
  },
});

export default VirtualFenceSetting;
